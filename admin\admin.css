/* Admin Panel Styles */
:root {
    --primary-color: #0081a7;
    --secondary-color: #00afb9;
    --background-color: #fdfcdc;
    --text-color: #333;
    --danger-color: #dc3545;
    --success-color: #28a745;
    --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --sidebar-width: 250px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: white;
    color: var(--text-color);
    line-height: 1.6;
}

/* Login Page Styles */
.container {
    max-width: 400px;
    margin: 80px auto;
    padding: 20px;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: var(--primary-color);
}

.logo a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.logo a:hover {
    color: var(--secondary-color);
    transform: scale(1.05);
}

.card {
    background: #f9f9f9;
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 30px;
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--primary-color);
}

input[type="email"],
input[type="password"],
.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
}

input[type="email"]:focus,
input[type="password"]:focus,
.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

.btn {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    width: 100%;
    margin-top: 10px;
}

.btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.alert {
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: none;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Dashboard Styles */
.dashboard {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--primary-color);
    color: white;
    position: fixed;
    height: 100%;
    overflow-y: auto;
    box-shadow: var(--shadow);
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.sidebar-header a {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.sidebar-header a:hover {
    color: var(--secondary-color);
    transform: scale(1.05);
}

.sidebar-header i {
    margin-right: 10px;
    color: var(--secondary-color);
}

.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.menu-item:hover, .menu-item.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--secondary-color);
}

.menu-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.header h1 {
    font-size: 1.8rem;
}

.user-info {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 8px 15px;
    border-radius: 30px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.user-info:hover {
    background-color: #f0f0f0;
    transform: translateY(-2px);
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 2px solid var(--primary-color);
    object-fit: cover;
}

.user-info span {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 15px;
}

.user-info .admin-email {
    color: #666;
    font-size: 0.85rem;
    margin-right: 15px;
}

.logout-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.logout-btn i {
    margin-right: 5px;
}

.logout-btn:hover {
    background-color: var(--danger-color);
    transform: translateY(-2px);
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: #f9f9f9;
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 25px;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
}

.card-icon.products {
    background-color: rgba(0, 129, 167, 0.1);
    color: var(--primary-color);
}

.card-icon.users {
    background-color: rgba(0, 175, 185, 0.1);
    color: var(--secondary-color);
}

.card-icon.orders {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.card-icon.revenue {
    background-color: rgba(111, 66, 193, 0.1);
    color: #6f42c1;
}

.card-info h3 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.card-info p {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* Table Styles */
.table-container {
    background-color: #f9f9f9;
    border-radius: 12px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
}

tr:hover {
    background-color: #f8f9fa;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 5px;
    font-size: 1rem;
}

.edit-btn {
    color: var(--secondary-color);
}

.delete-btn {
    color: var(--danger-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #f9f9f9;
    margin: 10% auto;
    padding: 25px;
    border-radius: 12px;
    width: 50%;
    max-width: 500px;
    box-shadow: var(--shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: black;
}

.modal-footer {
    padding-top: 15px;
    text-align: right;
}

/* Additional Dashboard Styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 90%;
        margin: 5% auto;
    }
}
