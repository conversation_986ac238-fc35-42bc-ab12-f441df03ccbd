<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Pawfect</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="admin.css">
</head>
<body>
    <div class="container">
        <div class="logo">
            <a href="../index.html">
                <i class="fas fa-paw" style="color: var(--secondary-color);"></i> Pawfect
            </a>
        </div>

        <div class="card">
            <h1>Admin Login</h1>

            <div id="alert" class="alert"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="btn">Login</button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const alert = document.getElementById('alert');

            try {
                // First try to authenticate with the API
                const response = await fetch('http://localhost:4001/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                // If API authentication fails, fall back to client-side authentication
                if (!response.ok) {
                    // Simple client-side authentication for demo purposes
                    if (email === '<EMAIL>' && password === 'admin123') {
                        // Store admin info in localStorage (same format as regular users)
                        localStorage.setItem('adminToken', 'demo-token');
                        localStorage.setItem('adminEmail', email);
                        localStorage.setItem('currentUser', JSON.stringify({
                            email: email,
                            name: 'Admin',
                            role: 'admin'
                        }));

                        // Show success message
                        alert.textContent = 'Login successful! Redirecting...';
                        alert.className = 'alert alert-success';
                        alert.style.display = 'block';

                        // Redirect to admin dashboard
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    } else {
                        // Show error message
                        alert.textContent = 'Invalid email or password';
                        alert.className = 'alert alert-danger';
                        alert.style.display = 'block';
                    }
                    return;
                }

                // If API authentication succeeds
                const data = await response.json();

                // Store admin token in localStorage (same format as regular users)
                localStorage.setItem('adminToken', data.token);
                localStorage.setItem('adminEmail', email);
                localStorage.setItem('currentUser', JSON.stringify({
                    email: email,
                    name: 'Admin',
                    role: 'admin'
                }));

                // Show success message
                alert.textContent = 'Login successful! Redirecting...';
                alert.className = 'alert alert-success';
                alert.style.display = 'block';

                // Redirect to admin dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } catch (error) {
                console.error('Login error:', error);

                // Try client-side authentication as fallback
                if (email === '<EMAIL>' && password === 'admin123') {
                    // Store admin info in localStorage (same format as regular users)
                    localStorage.setItem('adminToken', 'demo-token');
                    localStorage.setItem('adminEmail', email);
                    localStorage.setItem('currentUser', JSON.stringify({
                        email: email,
                        name: 'Admin',
                        role: 'admin'
                    }));

                    // Show success message
                    alert.textContent = 'Login successful! Redirecting...';
                    alert.className = 'alert alert-success';
                    alert.style.display = 'block';

                    // Redirect to admin dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    // Show error message
                    alert.textContent = 'Invalid email or password';
                    alert.className = 'alert alert-danger';
                    alert.style.display = 'block';
                }
            }
        });
    </script>
</body>
</html>
